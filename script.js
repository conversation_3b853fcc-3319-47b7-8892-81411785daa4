let display = document.getElementById('display');
let currentInput = '';
let operator = '';
let previousInput = '';
let shouldResetDisplay = false;

// Initialize display
display.value = '0';

function appendToDisplay(value) {
    if (shouldResetDisplay) {
        display.value = '';
        shouldResetDisplay = false;
    }
    
    if (display.value === '0' && value !== '.') {
        display.value = value;
    } else {
        display.value += value;
    }
}

function clearDisplay() {
    display.value = '0';
    currentInput = '';
    operator = '';
    previousInput = '';
    shouldResetDisplay = false;
}

function clearEntry() {
    display.value = '0';
}

function deleteLast() {
    if (display.value.length > 1) {
        display.value = display.value.slice(0, -1);
    } else {
        display.value = '0';
    }
}

function calculate() {
    try {
        let expression = display.value;
        
        // Replace display symbols with JavaScript operators
        expression = expression.replace(/×/g, '*');
        expression = expression.replace(/÷/g, '/');
        
        // Validate the expression
        if (isValidExpression(expression)) {
            let result = eval(expression);
            
            // Handle division by zero
            if (!isFinite(result)) {
                display.value = 'Error';
                shouldResetDisplay = true;
                return;
            }
            
            // Format the result
            if (result % 1 === 0) {
                display.value = result.toString();
            } else {
                display.value = parseFloat(result.toFixed(10)).toString();
            }
            
            shouldResetDisplay = true;
        } else {
            display.value = 'Error';
            shouldResetDisplay = true;
        }
    } catch (error) {
        display.value = 'Error';
        shouldResetDisplay = true;
    }
}

function isValidExpression(expression) {
    // Check for valid characters only
    const validChars = /^[0-9+\-*/.() ]+$/;
    if (!validChars.test(expression)) {
        return false;
    }
    
    // Check for balanced parentheses
    let parenthesesCount = 0;
    for (let char of expression) {
        if (char === '(') parenthesesCount++;
        if (char === ')') parenthesesCount--;
        if (parenthesesCount < 0) return false;
    }
    
    return parenthesesCount === 0;
}

// Keyboard support
document.addEventListener('keydown', function(event) {
    const key = event.key;
    
    if (key >= '0' && key <= '9') {
        appendToDisplay(key);
    } else if (key === '.') {
        appendToDisplay('.');
    } else if (key === '+') {
        appendToDisplay('+');
    } else if (key === '-') {
        appendToDisplay('-');
    } else if (key === '*') {
        appendToDisplay('*');
    } else if (key === '/') {
        event.preventDefault(); // Prevent browser search
        appendToDisplay('/');
    } else if (key === 'Enter' || key === '=') {
        calculate();
    } else if (key === 'Escape') {
        clearDisplay();
    } else if (key === 'Backspace') {
        deleteLast();
    }
});

// Prevent multiple decimal points in a number
function appendToDisplay(value) {
    if (shouldResetDisplay) {
        display.value = '';
        shouldResetDisplay = false;
    }
    
    // Prevent multiple decimal points
    if (value === '.') {
        const currentNumber = display.value.split(/[+\-*/]/).pop();
        if (currentNumber.includes('.')) {
            return;
        }
    }
    
    if (display.value === '0' && value !== '.') {
        display.value = value;
    } else {
        display.value += value;
    }
}

// DOM Elements
let display = document.getElementById('display');
let history = document.getElementById('history');
let scientificPanel = document.getElementById('scientificPanel');
let mainMenu = document.getElementById('mainMenu');
let calculatorContainer = document.getElementById('calculatorContainer');
let calculatorTitle = document.getElementById('calculatorTitle');
let leftParen = document.getElementById('leftParen');
let rightParen = document.getElementById('rightParen');

// State variables
let currentMode = 'basic';
let shouldResetDisplay = false;
let lastResult = null;

// Initialize display when calculator is shown
function initializeDisplay() {
    if (display) {
        display.value = '0';
    }
}

// Mathematical constants
const CONSTANTS = {
    'π': Math.PI,
    'e': Math.E
};

// Scientific functions
const FUNCTIONS = {
    'sin': Math.sin,
    'cos': Math.cos,
    'tan': Math.tan,
    'asin': Math.asin,
    'acos': Math.acos,
    'atan': Math.atan,
    'log': Math.log10,
    'ln': Math.log,
    'sqrt': Math.sqrt,
    'abs': Math.abs
};

// Navigation functions
function showCalculator(mode) {
    currentMode = mode;
    mainMenu.style.display = 'none';
    calculatorContainer.style.display = 'flex';

    if (mode === 'scientific') {
        calculatorTitle.textContent = 'Scientific Calculator';
        scientificPanel.style.display = 'grid';
        leftParen.style.display = 'block';
        rightParen.style.display = 'block';
    } else {
        calculatorTitle.textContent = 'Basic Calculator';
        scientificPanel.style.display = 'none';
        leftParen.style.display = 'none';
        rightParen.style.display = 'none';
    }

    initializeDisplay();
}

function showMainMenu() {
    mainMenu.style.display = 'block';
    calculatorContainer.style.display = 'none';
    clearDisplay();
}

function appendToDisplay(value) {
    if (shouldResetDisplay) {
        display.value = '';
        shouldResetDisplay = false;
    }

    if (display.value === '0' && value !== '.') {
        display.value = value;
    } else {
        display.value += value;
    }
}

function appendFunction(func) {
    if (shouldResetDisplay) {
        display.value = '';
        shouldResetDisplay = false;
    }

    if (display.value === '0') {
        display.value = func;
    } else {
        display.value += func;
    }
}

function appendConstant(constant) {
    if (shouldResetDisplay) {
        display.value = '';
        shouldResetDisplay = false;
    }

    if (display.value === '0') {
        display.value = constant;
    } else {
        display.value += constant;
    }
}

function appendOperator(op) {
    if (shouldResetDisplay) {
        shouldResetDisplay = false;
    }

    display.value += op;
}

function clearDisplay() {
    if (display) {
        display.value = '0';
    }
    if (history) {
        history.textContent = '';
    }
    shouldResetDisplay = false;
    lastResult = null;
}

function clearEntry() {
    if (display) {
        display.value = '0';
    }
}

function deleteLast() {
    if (display && display.value.length > 1) {
        display.value = display.value.slice(0, -1);
    } else if (display) {
        display.value = '0';
    }
}

function calculate() {
    try {
        let expression = display.value;
        let originalExpression = expression;

        // Store the expression in history
        history.textContent = originalExpression + ' =';

        // Replace display symbols with JavaScript operators
        expression = expression.replace(/×/g, '*');
        expression = expression.replace(/÷/g, '/');
        expression = expression.replace(/\^/g, '**');

        // Replace constants
        for (let constant in CONSTANTS) {
            expression = expression.replace(new RegExp(constant, 'g'), CONSTANTS[constant]);
        }

        // Handle factorial
        expression = expression.replace(/(\d+(?:\.\d+)?)!/g, (match, num) => {
            return factorial(parseFloat(num));
        });

        // Handle percentage
        expression = expression.replace(/(\d+(?:\.\d+)?)%/g, (match, num) => {
            return parseFloat(num) / 100;
        });

        // Process scientific functions
        expression = processScientificFunctions(expression);

        // Validate and calculate
        if (isValidExpression(expression)) {
            let result = eval(expression);

            // Handle special cases
            if (!isFinite(result)) {
                display.value = 'Error';
                shouldResetDisplay = true;
                return;
            }

            // Format the result
            lastResult = result;
            if (Math.abs(result) < 1e-10) {
                result = 0; // Handle floating point precision issues
            }

            if (result % 1 === 0 && Math.abs(result) < 1e15) {
                display.value = result.toString();
            } else {
                display.value = parseFloat(result.toPrecision(12)).toString();
            }

            shouldResetDisplay = true;
        } else {
            display.value = 'Error';
            shouldResetDisplay = true;
        }
    } catch (error) {
        display.value = 'Error';
        shouldResetDisplay = true;
    }
}

function processScientificFunctions(expression) {
    // Process functions that need special handling
    for (let func in FUNCTIONS) {
        const regex = new RegExp(`${func}\\(([^)]+)\\)`, 'g');
        expression = expression.replace(regex, (match, arg) => {
            try {
                let argValue = eval(arg);
                let result = FUNCTIONS[func](argValue);
                return result;
            } catch (e) {
                return 'NaN';
            }
        });
    }
    return expression;
}

function factorial(n) {
    if (n < 0 || n !== Math.floor(n)) return NaN;
    if (n === 0 || n === 1) return 1;
    if (n > 170) return Infinity; // Prevent overflow

    let result = 1;
    for (let i = 2; i <= n; i++) {
        result *= i;
    }
    return result;
}

function isValidExpression(expression) {
    // More comprehensive validation for scientific calculator
    const validChars = /^[0-9+\-*/.()e\s]+$/;
    if (!validChars.test(expression)) {
        return false;
    }

    // Check for balanced parentheses
    let parenthesesCount = 0;
    for (let char of expression) {
        if (char === '(') parenthesesCount++;
        if (char === ')') parenthesesCount--;
        if (parenthesesCount < 0) return false;
    }

    return parenthesesCount === 0;
}

// Enhanced keyboard support for scientific calculator
document.addEventListener('keydown', function(event) {
    const key = event.key;

    if (key >= '0' && key <= '9') {
        appendToDisplay(key);
    } else if (key === '.') {
        appendToDisplay('.');
    } else if (key === '+') {
        appendToDisplay('+');
    } else if (key === '-') {
        appendToDisplay('-');
    } else if (key === '*') {
        appendToDisplay('*');
    } else if (key === '/') {
        event.preventDefault(); // Prevent browser search
        appendToDisplay('/');
    } else if (key === '(' || key === ')') {
        appendToDisplay(key);
    } else if (key === '^') {
        appendOperator('^');
    } else if (key === '%') {
        appendOperator('%');
    } else if (key === 'Enter' || key === '=') {
        calculate();
    } else if (key === 'Escape') {
        clearDisplay();
    } else if (key === 'Backspace') {
        deleteLast();
    } else if (key.toLowerCase() === 's' && event.ctrlKey) {
        event.preventDefault();
        toggleMode();
    }
});

// Enhanced appendToDisplay with decimal point validation
function appendToDisplay(value) {
    if (shouldResetDisplay) {
        display.value = '';
        shouldResetDisplay = false;
    }

    // Prevent multiple decimal points in a single number
    if (value === '.') {
        const parts = display.value.split(/[+\-*/()^%]/);
        const lastPart = parts[parts.length - 1];
        if (lastPart.includes('.')) {
            return;
        }
    }

    if (display.value === '0' && value !== '.') {
        display.value = value;
    } else {
        display.value += value;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Show main menu by default
    if (mainMenu) {
        mainMenu.style.display = 'block';
    }
    if (calculatorContainer) {
        calculatorContainer.style.display = 'none';
    }
});

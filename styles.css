* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #667eea 50%, #764ba2 75%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
    overflow-x: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Main Menu Styles */
.main-menu {
    width: 100%;
    max-width: 800px;
    text-align: center;
}

.menu-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 40px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.app-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40px;
}

.menu-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.option-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
}

.option-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.25);
}

.option-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.option-card h3 {
    font-size: 1.8rem;
    color: white;
    margin-bottom: 10px;
    font-weight: 600;
}

.option-card p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
    font-size: 1rem;
}

.features {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.features span {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-align: left;
}

/* Calculator Container */
.calculator-container {
    width: 100%;
    max-width: 500px;
}

.calculator {
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 25px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
    transition: all 0.3s ease;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.back-btn {
    background: rgba(231, 76, 60, 0.8);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.back-btn:hover {
    background: rgba(192, 57, 43, 0.9);
    transform: translateX(-2px);
}

#calculatorTitle {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.display {
    margin-bottom: 20px;
}

.history {
    background: rgba(52, 73, 94, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px 15px 0 0;
    color: #bdc3c7;
    font-size: 0.9rem;
    text-align: right;
    padding: 12px 20px;
    min-height: 30px;
    border-bottom: 1px solid rgba(44, 62, 80, 0.5);
}

#display {
    width: 100%;
    height: 85px;
    background: rgba(52, 73, 94, 0.8);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 0 0 15px 15px;
    color: white;
    font-size: 2.2rem;
    text-align: right;
    padding: 0 20px;
    outline: none;
    box-shadow: inset 0 3px 15px rgba(0, 0, 0, 0.2);
    font-weight: 300;
}

.scientific-panel {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin-bottom: 15px;
    opacity: 1;
    max-height: 200px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.scientific-panel.hidden {
    opacity: 0;
    max-height: 0;
    margin-bottom: 0;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.btn {
    height: 65px;
    border: none;
    border-radius: 15px;
    font-size: 1.3rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(-1px);
}

.number {
    background: rgba(52, 152, 219, 0.8);
    color: white;
}

.number:hover {
    background: rgba(41, 128, 185, 0.9);
}

.operator {
    background: rgba(230, 126, 34, 0.8);
    color: white;
}

.operator:hover {
    background: rgba(211, 84, 0, 0.9);
}

.equals {
    background: rgba(39, 174, 96, 0.8);
    color: white;
    grid-column: span 4;
}

.equals:hover {
    background: rgba(34, 153, 84, 0.9);
}

.clear {
    background: rgba(231, 76, 60, 0.8);
    color: white;
}

.clear:hover {
    background: rgba(192, 57, 43, 0.9);
}

.scientific {
    background: rgba(142, 68, 173, 0.8);
    color: white;
    font-size: 1rem;
    height: 50px;
}

.scientific:hover {
    background: rgba(155, 89, 182, 0.9);
}

.scientific:hover {
    background: #9b59b6;
}

.equals {
    grid-column: span 4;
}

/* Responsive design */
@media (max-width: 768px) {
    .menu-container {
        padding: 30px 20px;
    }

    .app-title {
        font-size: 2.5rem;
    }

    .menu-options {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .option-card {
        padding: 25px;
    }
}

@media (max-width: 480px) {
    .calculator {
        padding: 20px;
        max-width: 100%;
    }

    .app-title {
        font-size: 2rem;
    }

    .option-card {
        padding: 20px;
    }

    .option-icon {
        font-size: 3rem;
    }

    #display {
        height: 75px;
        font-size: 1.8rem;
    }

    .btn {
        height: 55px;
        font-size: 1.1rem;
    }

    .scientific {
        height: 45px;
        font-size: 0.9rem;
    }

    .buttons {
        gap: 10px;
    }

    .scientific-panel {
        gap: 8px;
    }

    .header {
        margin-bottom: 15px;
    }

    #calculatorTitle {
        font-size: 1.3rem;
    }
}

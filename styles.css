* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.calculator {
    background: #2c3e50;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 350px;
    width: 100%;
}

.display {
    margin-bottom: 20px;
}

#display {
    width: 100%;
    height: 80px;
    background: #34495e;
    border: none;
    border-radius: 10px;
    color: white;
    font-size: 2rem;
    text-align: right;
    padding: 0 20px;
    outline: none;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.btn {
    height: 60px;
    border: none;
    border-radius: 10px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:active {
    transform: translateY(0);
}

.number {
    background: #3498db;
    color: white;
}

.number:hover {
    background: #2980b9;
}

.operator {
    background: #e67e22;
    color: white;
}

.operator:hover {
    background: #d35400;
}

.equals {
    background: #27ae60;
    color: white;
    grid-column: span 2;
}

.equals:hover {
    background: #229954;
}

.clear {
    background: #e74c3c;
    color: white;
}

.clear:hover {
    background: #c0392b;
}

.zero {
    grid-column: span 2;
}

/* Responsive design */
@media (max-width: 480px) {
    .calculator {
        padding: 15px;
        max-width: 300px;
    }
    
    #display {
        height: 70px;
        font-size: 1.8rem;
    }
    
    .btn {
        height: 50px;
        font-size: 1rem;
    }
    
    .buttons {
        gap: 10px;
    }
}
